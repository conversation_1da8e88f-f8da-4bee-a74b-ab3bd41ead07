import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class GetShippingLabelDto {
  @ApiProperty({
    description: 'Internal order ID to get shipping label for',
    example: 123,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  orderId: number;
}

export class ShippingLabelResponseDto {
  @ApiProperty({
    description: 'Shipping document URL from TikTok Shop (valid for 24 hours)',
    example: 'https://example.com/shipping-label.pdf',
    required: false,
  })
  R2docUrl?: string | null;

  @ApiProperty({
    description: 'Shipping document URL from TikTok Shop (valid for 24 hours)',
    example: 'https://example.com/shipping-label.pdf',
    required: false,
  })
  docUrl?: string | null;

  @ApiProperty({
    description: 'Package tracking number from the shipping carrier',
    example: 'TT123456789',
    required: false,
  })
  trackingNumber?: string | null;

  @ApiProperty({
    description: 'TikTok Shop package ID used for the request',
    example: '1154528977690660930',
  })
  packageIdTT: string;

  @ApiProperty({
    description: 'Internal order ID',
    example: 123,
  })
  orderId: number;

  /*
  @ApiProperty({
    description: 'Complete raw TikTok response from shipping document API call',
  })
  rawTikTokResponse: any;
  */
}
