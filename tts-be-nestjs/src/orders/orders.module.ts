import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { Order, OrderLineItem, TikTokPackage } from './entities';
import { TikTokOrderMapper } from './mappers';
import { TiktokShopModule } from '../tiktok-shop/tiktok-shop.module';
import { AuthModule } from '../auth/auth.module';
import { QueuesModule } from '../queues';
import { TikTokShop } from '../tiktok-shop/entities/tiktok-shop.entity';
import { User } from '../auth/entities/user.entity';
import { CloudStorageModule } from '../common/cloud-storage/cloud-storage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      OrderLineItem,
      TikTokPackage,
      TikTokShop,
      User,
    ]),
    forwardRef(() => TiktokShopModule),
    AuthModule,
    QueuesModule,
    CloudStorageModule,
  ],
  controllers: [
    OrdersController,
  ],
  providers: [
    OrdersService,
    TikTokOrderMapper,
  ],
  exports: [
    OrdersService,
    TikTokOrderMapper,
  ],
})
export class OrdersModule {}
